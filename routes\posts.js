const express = require('express');
const router = express.Router();
const { createPost, getPosts, updatePostDecision } = require('../controllers/postController');
const { protect, admin } = require('../middleware/authMiddleware');

router.post('/post',protect,  createPost);
router.get('/getPosts', protect, admin, getPosts);
router.put('/updatePostDecision', protect, admin, updatePostDecision);

module.exports = router;