const Post = require('../models/Post');
const axios = require('axios');

// @desc    Create a new post with moderation
// @route   POST /api/posts
// @access  Private
exports.createPost = async (req, res) => {
  try {
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({ message: 'Content is required' });
    }

    // Send content to Python API for prediction
    const response = await axios.post('http://127.0.0.1:8000/predict', {
      text: content
    });

    const { toxicity, sentiment } = response.data;

    // Apply rule logic
    let finalDecision = 'allowed';
    if (toxicity === 'toxic') {
      finalDecision = 'removed';
    } else if (sentiment.toLowerCase().includes('negative')) {
      finalDecision = 'flagged';
    }

    // Create post
    const post = await Post.create({
      content,
      userId: req.user._id,
      toxicity,
      sentiment,
      finalDecision
    });

    res.status(201).json(post);
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error processing prediction', error: error.message });
  }
};


// @desc    Get all posts
// @route   GET /api/posts
// @access  Private/Admin
exports.getPosts = async (req, res) => {
  try {
    const posts = await Post.find().populate('userId', 'name email');
    res.json(posts);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// @desc    Update post final decision
// @route   PUT /api/posts/:id/decision
// @access  Private/Admin
exports.updatePostDecision = async (req, res) => {
  try {
    const { id } = req.params;
    const { finalDecision } = req.body;

    // Validate finalDecision value
    if (!finalDecision || !['allowed', 'flagged', 'removed'].includes(finalDecision)) {
      return res.status(400).json({
        message: 'Invalid finalDecision. Must be one of: allowed, flagged, removed'
      });
    }

    // Find and update the post
    const post = await Post.findById(id);
    if (!post) {
      return res.status(404).json({ message: 'Post not found' });
    }

    // Update the finalDecision
    post.finalDecision = finalDecision;
    await post.save();

    // Return the updated post with user information
    const updatedPost = await Post.findById(id).populate('userId', 'name email');

    res.json({
      message: 'Post decision updated successfully',
      post: updatedPost
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Error updating post decision', error: error.message });
  }
};